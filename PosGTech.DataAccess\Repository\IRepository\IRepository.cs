﻿using System.Linq.Expressions;

namespace PosGTech.DataAccess.Repository.IRepository
{
    public interface IRepository<T> where T : class
    {
        Task<T?> GetFirstOrDefault(Expression<Func<T, bool>>? filter = null, string? includeProperties = null, bool tracked = true, Expression<Func<T, object>>? order = null);
        Task<T?> GetLastOrDefault(Expression<Func<T, bool>>? filter = null, string? includeProperties = null, bool tracked = true, Expression<Func<T, object>>? order = null);
        Task<IEnumerable<T>> GetAll(Expression<Func<T, bool>>? filter = null, string? includeProperties = null, Expression<Func<T, object>>? order = null);
        Task<IEnumerable<TR>> Select<TR>(Expression<Func<T, TR>> select, Expression<Func<T, bool>>? filter = null, string? includeProperties = null) where TR : class;
        Task<T?> GetByIdAsync(Object id);
        bool Any(Expression<Func<T, bool>> filter);
        bool All(Expression<Func<T, bool>> filter);
        void Add(T entity);
        void AddRange(List<T> entity);
        void Update(T entity);
        void Remove(T entity);
        void RemoveRange(IEnumerable<T> entity);
    }
}
