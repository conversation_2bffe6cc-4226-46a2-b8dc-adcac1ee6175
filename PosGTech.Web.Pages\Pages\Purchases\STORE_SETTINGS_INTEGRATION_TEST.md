# اختبار تكامل إعدادات المتجر مع نظام الطباعة في صفحة الشراء

## نظرة عامة
هذا الدليل يوضح كيفية اختبار التكامل الكامل بين إعدادات المتجر المدخلة في `UpsertStoreSettings.razor` والبيانات المعروضة في فواتير الشراء المطبوعة من `UpsertPurchase.razor`.

## التحسينات المطبقة

### 1. تحسين دالة LoadDefaultShopSettings() ✅
```csharp
// قبل التحسين - استخدام GetAll خطأ
var response = await _shopSettings.GetAll("ShopSettings/getDefaultShopSettings");

// بعد التحسين - استخدام endpoint الصحيح
var response = await _shopSettings.GetByIdAsync("ShopSettings/getDefaultShopSettings", Guid.Empty);
```

### 2. إضافة RefreshShopSettings() ✅
```csharp
/// <summary>
/// إعادة تحميل إعدادات المتجر لضمان الحصول على أحدث البيانات
/// </summary>
private async Task RefreshShopSettings()
{
    await LoadDefaultShopSettings();
}
```

### 3. تحديث دوال الطباعة والمعاينة ✅
```csharp
async Task Print()
{
    // إعادة تحميل إعدادات المتجر قبل الطباعة
    await RefreshShopSettings();
    await PrintWithFormat("auto");
}

private async Task PreviewPurchase()
{
    // إعادة تحميل إعدادات المتجر قبل المعاينة
    await RefreshShopSettings();
    // باقي الكود...
}
```

### 4. تحسين معالجة مسار الشعار ✅
```csharp
/// <summary>
/// تحويل مسار الشعار النسبي إلى URL كامل للـ API
/// </summary>
private string GetFullLogoUrl(string? logoPath)
{
    if (string.IsNullOrEmpty(logoPath)) return string.Empty;
    
    // معالجة المسارات المختلفة (http, https, data:, relative)
    if (logoPath.StartsWith("http://") || logoPath.StartsWith("https://"))
        return logoPath;
    
    if (logoPath.StartsWith("data:"))
        return logoPath;
    
    var apiBaseUrl = "https://localhost:7282";
    var fullPath = logoPath.StartsWith("/") ? logoPath : "/" + logoPath;
    return apiBaseUrl + fullPath;
}
```

## خطوات الاختبار الشاملة

### المرحلة 1: اختبار إعدادات المتجر الأساسية

#### 1.1 إنشاء/تحديث إعدادات المتجر
1. **افتح صفحة إعدادات المتجر**: `/upsertStoreSettings/{id}`
2. **أدخل البيانات التالية**:
   - **اسم المتجر**: "متجر الإلكترونيات الحديثة"
   - **اسم الشركة**: "شركة التقنية المتقدمة"
   - **رقم الهاتف**: "+966501234567"
   - **العنوان**: "الرياض، حي الملك فهد، شارع الملك عبدالعزيز"
3. **ارفع شعار الشركة**: اختر صورة بصيغة PNG أو JPG
4. **احفظ الإعدادات** وتأكد من ظهور رسالة النجاح

#### 1.2 التحقق من حفظ البيانات
1. **أعد فتح صفحة الإعدادات**
2. **تأكد من ظهور جميع البيانات المحفوظة**
3. **تأكد من ظهور الشعار المرفوع**

### المرحلة 2: اختبار التكامل مع فواتير الشراء

#### 2.1 اختبار الطباعة الفورية
1. **افتح صفحة فاتورة شراء موجودة**: `/upsertPurchase/{id}`
2. **اضغط على زر "طباعة"**
3. **تحقق من ظهور البيانات التالية في الفاتورة**:
   - ✅ **الشعار**: يجب أن يظهر في الزاوية اليمنى العلوية
   - ✅ **اسم المتجر**: يجب أن يظهر في وسط الهيدر
   - ✅ **اسم الشركة**: يجب أن يظهر تحت اسم المتجر
   - ✅ **رقم الهاتف**: يجب أن يظهر في تفاصيل الشركة
   - ✅ **العنوان**: يجب أن يظهر في تفاصيل الشركة
   - ✅ **معلومات الفوتر**: يجب أن تظهر في أسفل الفاتورة

#### 2.2 اختبار المعاينة
1. **اضغط على زر "معاينة الفاتورة"**
2. **تأكد من فتح نافذة جديدة مع الفاتورة**
3. **تحقق من نفس البيانات المذكورة أعلاه**
4. **اختبر أزرار "طباعة" و "إغلاق" في نافذة المعاينة**

### المرحلة 3: اختبار التحديث الفوري

#### 3.1 تحديث إعدادات المتجر
1. **افتح صفحة إعدادات المتجر مرة أخرى**
2. **غيّر البيانات التالية**:
   - **اسم المتجر**: "متجر الإلكترونيات المطور"
   - **رقم الهاتف**: "+966507654321"
   - **العنوان**: "جدة، حي الروضة، شارع التحلية"
3. **ارفع شعار جديد** (اختياري)
4. **احفظ التغييرات**

#### 3.2 التحقق من التحديث الفوري
1. **ارجع إلى صفحة فاتورة الشراء** (بدون إعادة تشغيل النظام)
2. **اضغط على زر "طباعة" أو "معاينة"**
3. **تأكد من ظهور البيانات المحدثة فوراً**:
   - ✅ **اسم المتجر الجديد**: "متجر الإلكترونيات المطور"
   - ✅ **رقم الهاتف الجديد**: "+966507654321"
   - ✅ **العنوان الجديد**: "جدة، حي الروضة، شارع التحلية"
   - ✅ **الشعار الجديد** (إذا تم تغييره)

### المرحلة 4: اختبار الحالات الاستثنائية

#### 4.1 اختبار عدم وجود إعدادات
1. **احذف جميع إعدادات المتجر** (من قاعدة البيانات مؤقتاً)
2. **افتح صفحة فاتورة الشراء**
3. **اضغط على زر "طباعة"**
4. **تأكد من ظهور القيم الافتراضية**:
   - "اسم المتجر"
   - "اسم الشركة"
   - "رقم الهاتف"
   - "عنوان المتجر"

#### 4.2 اختبار الشعار المفقود
1. **أنشئ إعدادات متجر بدون شعار**
2. **اضغط على زر "طباعة"**
3. **تأكد من ظهور نص "شعار الشركة" بدلاً من الصورة**

#### 4.3 اختبار مسارات الشعار المختلفة
1. **اختبر شعار بمسار نسبي**: `/logo/image.png`
2. **اختبر شعار بمسار كامل**: `https://example.com/logo.png`
3. **اختبر شعار base64**: `data:image/png;base64,...`
4. **تأكد من عمل جميع الأنواع بشكل صحيح**

## نتائج الاختبار المتوقعة

### ✅ التكامل الناجح يجب أن يحقق:
1. **تحديث فوري**: أي تغيير في إعدادات المتجر يظهر فوراً في الفواتير
2. **عرض صحيح**: جميع البيانات تظهر في المواقع الصحيحة
3. **معالجة الأخطاء**: القيم الافتراضية تظهر عند عدم وجود إعدادات
4. **دعم الشعارات**: جميع أنواع مسارات الشعارات تعمل بشكل صحيح
5. **تصميم متسق**: التصميم يحافظ على الشكل الاحترافي

### ❌ علامات فشل التكامل:
1. **بيانات قديمة**: ظهور بيانات قديمة بعد التحديث
2. **شعار مفقود**: عدم ظهور الشعار أو ظهور رابط مكسور
3. **قيم فارغة**: ظهور حقول فارغة بدلاً من القيم الافتراضية
4. **أخطاء JavaScript**: أخطاء في Console المتصفح
5. **تصميم مكسور**: عدم ظهور العناصر في المواقع الصحيحة

## استكشاف الأخطاء

### مشكلة: البيانات لا تتحدث فوراً
**الحل**: تحقق من:
```javascript
// في Console المتصفح
console.log('Shop settings loaded:', _defaultShopSettings);
```

### مشكلة: الشعار لا يظهر
**الحل**: تحقق من:
1. مسار الشعار في قاعدة البيانات
2. URL الأساسي للـ API في `GetFullLogoUrl()`
3. صلاحيات الوصول للملف

### مشكلة: أخطاء في الطباعة
**الحل**: تحقق من:
```javascript
// في Console المتصفح
console.log('Print functions check:', {
    printReceiptFromBlazor: typeof printReceiptFromBlazor,
    previewReceiptFromBlazor: typeof previewReceiptFromBlazor,
    printJS: typeof printJS
});
```

## الخلاصة
هذا الاختبار يضمن التكامل الكامل بين إعدادات المتجر ونظام الطباعة، مما يوفر تجربة مستخدم متسقة ومحدثة فوراً دون الحاجة لإعادة تشغيل النظام.

---

**تاريخ الإنشاء**: 2025-07-15  
**آخر تحديث**: 2025-07-15  
**الحالة**: جاهز للاختبار ✅
