# قائمة اختبار الطباعة المحسنة - UpsertPurchase.razor

## اختبار سريع للتحقق من حل مشكلة الصفحة الثانية الفارغة

### ✅ الاختبار الأساسي (5 دقائق)

#### 1. اختبار الطباعة المباشرة
```
□ افتح صفحة فاتورة شراء موجودة
□ اضغط على زر "طباعة"
□ تحقق من معاينة الطباعة:
  □ صفحة واحدة فقط ✅
  □ لا توجد صفحة ثانية فارغة ✅
  □ جميع العناصر مرئية ✅
  □ النص مقروء وواضح ✅
```

#### 2. اختبار المعاينة
```
□ اضغط على زر "معاينة الفاتورة"
□ تحقق من النافذة الجديدة:
  □ المحتوى يتناسب مع الصفحة ✅
  □ لا يوجد تمرير عمودي مفرط ✅
  □ العناصر منظمة ومتوازنة ✅
  □ أزرار "طباعة" و "إغلاق" تعمل ✅
```

#### 3. اختبار التصميم
```
□ تحقق من العناصر التالية:
  □ الشعار في الزاوية اليمنى (50×50px) ✅
  □ اسم المتجر في الوسط (16px) ✅
  □ التاريخ في الزاوية اليسرى ✅
  □ جدول الأصناف مقروء (9-10px) ✅
  □ قسم الإجماليات منظم ✅
  □ الفوتر في الأسفل ✅
```

### 🔧 اختبار متقدم (10 دقائق)

#### 4. اختبار المتصفحات
```
□ Chrome:
  □ طباعة ✅  □ معاينة ✅  □ تصميم ✅
□ Firefox:
  □ طباعة ✅  □ معاينة ✅  □ تصميم ✅
□ Edge:
  □ طباعة ✅  □ معاينة ✅  □ تصميم ✅
```

#### 5. اختبار أحجام البيانات المختلفة
```
□ فاتورة بـ 3 أصناف:
  □ صفحة واحدة ✅  □ تصميم متوازن ✅
□ فاتورة بـ 10 أصناف:
  □ صفحة واحدة ✅  □ جدول مقروء ✅
□ فاتورة بـ 15 صنف:
  □ صفحة واحدة ✅  □ لا تجاوز للحدود ✅
```

#### 6. اختبار إعدادات المتجر
```
□ مع شعار:
  □ الشعار يظهر بوضوح ✅
  □ لا يؤثر على التخطيط ✅
□ بدون شعار:
  □ نص "شعار الشركة" يظهر ✅
  □ التخطيط متوازن ✅
□ أسماء طويلة:
  □ النص لا يتجاوز الحدود ✅
  □ التخطيط محافظ عليه ✅
```

### 🐛 استكشاف الأخطاء

#### مشكلة: لا تزال تظهر صفحة ثانية
**الحلول المحتملة**:
```javascript
// 1. تحقق من أبعاد الحاوية في Console
console.log('Container height:', 
  document.querySelector('.invoice-container').offsetHeight);

// 2. تحقق من إعدادات الطباعة
console.log('Print styles loaded:', 
  !!document.querySelector('style[data-print]'));

// 3. فحص CSS
console.log('Max height applied:', 
  getComputedStyle(document.querySelector('.invoice-container')).maxHeight);
```

#### مشكلة: النص صغير جداً
**التحقق**:
```
□ حجم الخط الأساسي 12px ✅
□ عناوين الجدول 10px ✅
□ محتوى الجدول 9px ✅
□ إذا كان صغيراً جداً، يمكن زيادة 1px لكل عنصر
```

#### مشكلة: العناصر متداخلة
**التحقق**:
```
□ padding الحاوية 8mm (شاشة) / 10mm (طباعة) ✅
□ margin بين العناصر مناسب ✅
□ لا يوجد overflow مخفي ✅
```

### 📊 مقاييس النجاح

#### ✅ النتائج المطلوبة
- **صفحة واحدة فقط**: لا توجد صفحات إضافية فارغة
- **محتوى مكتمل**: جميع العناصر مرئية ومقروءة
- **تصميم احترافي**: ألوان وتخطيط منظم
- **دعم عربي**: RTL وخطوط عربية تعمل بشكل صحيح
- **سرعة التحميل**: معاينة وطباعة سريعة

#### 📏 الأبعاد المستهدفة
- **عرض الحاوية**: 190mm
- **ارتفاع الحاوية**: ≤ 277mm
- **حجم الخط الأساسي**: 12px
- **حجم خط الجدول**: 9-10px
- **مساحة الحشو**: 8-10mm

### 🚀 اختبار الأداء

#### سرعة التحميل
```
□ معاينة تفتح خلال < 2 ثانية ✅
□ طباعة تبدأ خلال < 3 ثواني ✅
□ لا توجد أخطاء في Console ✅
```

#### استهلاك الذاكرة
```
□ لا توجد تسريبات ذاكرة ✅
□ النوافذ تُغلق بشكل صحيح ✅
□ الصور تُحمل بكفاءة ✅
```

### 📝 تقرير الاختبار

#### نموذج تقرير سريع
```
تاريخ الاختبار: ___________
المتصفح المستخدم: ___________
نظام التشغيل: ___________

النتائج:
□ الطباعة تعمل بصفحة واحدة ✅
□ المعاينة تعمل بشكل صحيح ✅
□ التصميم احترافي ومقروء ✅
□ دعم اللغة العربية يعمل ✅
□ إعدادات المتجر تظهر ✅

ملاحظات إضافية:
_________________________________
_________________________________

التقييم العام: ⭐⭐⭐⭐⭐
```

### 🔄 اختبار دوري

#### أسبوعياً
- اختبار الطباعة الأساسي
- فحص أي تحديثات في المتصفحات

#### شهرياً  
- اختبار شامل لجميع المتصفحات
- فحص الأداء والسرعة
- مراجعة ملاحظات المستخدمين

#### عند التحديثات
- اختبار فوري بعد أي تعديل على CSS
- فحص التوافق مع التحديثات الجديدة
- التأكد من عدم كسر الوظائف الموجودة

---

**آخر تحديث**: 2025-07-15  
**الحالة**: جاهز للاختبار ✅  
**المدة المتوقعة**: 5-15 دقيقة حسب نوع الاختبار
