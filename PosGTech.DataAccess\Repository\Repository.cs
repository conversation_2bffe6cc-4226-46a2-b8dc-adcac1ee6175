﻿using Microsoft.EntityFrameworkCore;
using PosGTech.DataAccess.Data;
using PosGTech.DataAccess.Repository.IRepository;
using System.Linq.Expressions;

namespace PosGTech.DataAccess.Repository
{
    public class Repository<T> : IRepository<T> where T : class
    {
        protected readonly ApplicationDbContext _db;
        internal DbSet<T> dbSet;

        public Repository(ApplicationDbContext db)
        {
            _db = db;
            this.dbSet = _db.Set<T>();
        }

        public void Add(T entity)
        {
            dbSet.Add(entity);
        }
        public void AddRange(List<T> entity)
        {
            dbSet.AddRange(entity);
        }
        public async Task<IEnumerable<T>> GetAll(Expression<Func<T, bool>>? filter = null, string? includeProperties = null, Expression<Func<T, object>>? order = null)
        {
            IQueryable<T> query = dbSet;
            if (filter != null)
            {
                query = query.Where(filter);
            }

            if (includeProperties != null)
            {
                foreach (var includeProp in includeProperties.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
                {
                    query = query.Include(includeProp);
                }
            }
            if (order != null)
            {
                query = query.OrderBy(order);
            }
            return await query.ToListAsync();
        }
        public async Task<IEnumerable<TR>> Select<TR>(Expression<Func<T, TR>> select, Expression<Func<T, bool>>? filter = null, string? includeProperties = null) where TR : class
        {
            IQueryable<T> query = dbSet;
            if (filter != null)
            {
                query = query.Where(filter);
            }
            if (includeProperties != null)
            {
                foreach (var includeProp in includeProperties.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
                {
                    query = query.Include(includeProp);
                }
            }
            return await query.Select(select).ToListAsync();
        }
        public async Task<T?> GetFirstOrDefault(Expression<Func<T, bool>>? filter = null, string? includeProperties = null, bool tracked = true, Expression<Func<T, object>>? order = null)
        {
            IQueryable<T> query;
            if (tracked) query = dbSet;
            else query = dbSet.AsNoTracking();
            if (includeProperties != null)
            {
                foreach (var includeProp in includeProperties.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
                {
                    query = query.Include(includeProp);
                }
            }
            if (order != null) query = query.OrderBy(order);
            if (filter != null) return await query.FirstOrDefaultAsync(filter);
            return await query.FirstOrDefaultAsync();

        }
        public async Task<T?> GetLastOrDefault(Expression<Func<T, bool>>? filter = null, string? includeProperties = null, bool tracked = true, Expression<Func<T, object>>? order = null)
        {
            IQueryable<T> query;
            if (tracked) query = dbSet;
            else query = dbSet.AsNoTracking();
            if (includeProperties != null)
            {
                foreach (var includeProp in includeProperties.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
                {
                    query = query.Include(includeProp);
                }
            }
            if (order != null) query = query.OrderBy(order);
            if (filter != null) return await query.LastOrDefaultAsync(filter);
            return await query.LastOrDefaultAsync();

        }

        public async Task<T?> GetByIdAsync(Object id)
        {
            return await dbSet.FindAsync(id);
        }
        public bool Any(Expression<Func<T, bool>> filter) => dbSet.Any(filter);
        public bool All(Expression<Func<T, bool>> filter) => dbSet.All(filter);
        public void Update(T entity)
        {
            dbSet.Update(entity);
        }
        public void Remove(T entity)
        {
            dbSet.Remove(entity);
        }

        public void RemoveRange(IEnumerable<T> entity)
        {
            dbSet.RemoveRange(entity);
        }
    }
}
