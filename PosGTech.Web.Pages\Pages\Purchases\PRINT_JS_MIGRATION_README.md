# تطبيق مكتبة Print.js في صفحة الشراء - UpsertPurchase.razor

## نظرة عامة
تم استبدال نظام الطباعة القديم القائم على RDLC بنظام جديد يستخدم مكتبة Print.js لتوفير تجربة طباعة محسنة ومتسقة مع صفحة الإيصالات.

## التغييرات المنجزة

### 1. تحديث UpsertPurchase.razor.cs ✅
- **إضافة مراجع جديدة**: `PosGTech.ModelsDTO.ShopSettings` و `NavigationManager`
- **إضافة متغيرات الطباعة**: `_defaultShopSettings`, `_isPrinting`, `_printButtonText`
- **إضافة Inject للخدمات**: `IGRepository<ShopSettingsDTO>` و `NavigationManager`
- **استبدال دالة Print القديمة** بنظام Print.js الجديد
- **إضافة دوال مساعدة جديدة**:
  - `LoadDefaultShopSettings()`: تحميل إعدادات المتجر
  - `PrintWithFormat()`: طباعة بتنسيق محدد
  - `ValidatePurchaseForPrint()`: التحقق من صحة البيانات قبل الطباعة
  - `PreviewPurchase()`: معاينة الفاتورة قبل الطباعة
  - `CheckJavaScriptReady()`: التحقق من تحميل دوال JavaScript
  - `PrintUsingFallbackMethod()`: طباعة بديلة عند عدم توفر Print.js
  - `PreviewUsingFallbackMethod()`: معاينة بديلة
  - `GeneratePurchaseInvoiceHtml()`: إنشاء HTML للفاتورة

### 2. إنشاء قوالب HTML/CSS لفواتير الشراء ✅
- **تصميم احترافي**: تخطيط A4 مع ألوان طبيعية ومناسبة للمكاتب
- **دعم اللغة العربية**: اتجاه RTL وخطوط عربية
- **تكامل مع إعدادات المتجر**: عرض شعار الشركة واسم المتجر والعنوان والهاتف
- **جدول تفصيلي للأصناف**: عرض جميع تفاصيل الأصناف المشتراة
- **قسم الإجماليات**: عرض الإجماليات والتخفيضات والمدفوع والمتبقي
- **تنسيقات الطباعة**: تحسينات خاصة للطباعة مع إخفاء العناصر غير المرغوبة

### 3. تحديث زر الطباعة في UpsertPurchase.razor ✅
- **إضافة زر معاينة**: زر جديد لمعاينة الفاتورة قبل الطباعة
- **تحديث زر الطباعة**: استخدام النظام الجديد مع حالات التحميل المحسنة
- **شروط العرض**: إظهار زر المعاينة فقط للفواتير الموجودة

### 4. حذف ملف PurchasReport.rdlc ✅
- **حذف الملف**: إزالة ملف التقرير RDLC القديم نهائياً
- **تحديث ملف المشروع**: إزالة مراجع الملف المحذوف من `PosGTech.Reports.csproj`
- **تعطيل endpoint القديم**: تعطيل `ReportPurchase` في `ReportController.cs`

## الميزات الجديدة

### 1. طباعة محسنة
- **استخدام Print.js**: مكتبة حديثة للطباعة مع دعم أفضل للمتصفحات
- **طريقة بديلة**: نظام احتياطي عند عدم توفر Print.js
- **تحقق من الجاهزية**: فحص تحميل دوال JavaScript قبل الطباعة

### 2. معاينة الفاتورة
- **معاينة تفاعلية**: إمكانية معاينة الفاتورة قبل الطباعة
- **أزرار تحكم**: أزرار طباعة وإغلاق في نافذة المعاينة
- **تصميم متجاوب**: عرض مناسب على الشاشة والطباعة

### 3. تكامل مع إعدادات المتجر
- **بيانات ديناميكية**: استخدام إعدادات المتجر الفعلية
- **شعار الشركة**: عرض شعار الشركة إذا كان متوفراً
- **معلومات الاتصال**: عرض اسم الشركة والعنوان والهاتف

### 4. تصميم احترافي
- **ألوان طبيعية**: تدرجات زرقاء وبيضاء مناسبة للمكاتب
- **تخطيط منظم**: هيدر وجدول وفوتر منظمين
- **خطوط عربية**: استخدام خط Cairo للنصوص العربية

## التحقق من صحة العمل

### 1. اختبار الطباعة
```javascript
// في Console المتصفح
console.log('Print functions check:', {
    printReceiptFromBlazor: typeof printReceiptFromBlazor,
    previewReceiptFromBlazor: typeof previewReceiptFromBlazor,
    printJS: typeof printJS
});
```

### 2. اختبار المعاينة
- افتح صفحة فاتورة شراء موجودة
- اضغط على زر "معاينة الفاتورة"
- تأكد من فتح نافذة جديدة مع الفاتورة
- اختبر أزرار الطباعة والإغلاق

### 3. اختبار الطباعة
- اضغط على زر "طباعة"
- تأكد من فتح حوار الطباعة
- تحقق من التنسيق والألوان

## الملفات المتأثرة

1. **PosGTech.Web.Pages/Pages/Purchases/UpsertPurchase.razor.cs** ✅
   - استبدال نظام الطباعة بالكامل
   - إضافة دوال جديدة للطباعة والمعاينة

2. **PosGTech.Web.Pages/Pages/Purchases/UpsertPurchase.razor** ✅
   - إضافة زر معاينة جديد
   - تحديث زر الطباعة

3. **PosGTech.Reports/PurchaseReports/PurchasReport.rdlc** ❌ (محذوف)
   - حذف ملف التقرير RDLC القديم

4. **PosGTech.Reports/PosGTech.Reports.csproj** ✅
   - إزالة مراجع الملف المحذوف

5. **PosGTech.API/Controllers/ReportController.cs** ✅
   - تعطيل endpoint الطباعة القديم

## الاعتمادات

### JavaScript Libraries
- **Print.js**: مكتبة الطباعة الأساسية
- **Google Fonts**: خطوط Cairo و Noto Sans Arabic

### CSS Frameworks
- **MudBlazor**: مكونات واجهة المستخدم
- **Custom CSS**: تنسيقات مخصصة للطباعة

## التطوير المستقبلي

### تحسينات محتملة
1. **خيارات طباعة متقدمة**: A5, 80mm thermal
2. **تخصيص التصميم**: إمكانية تخصيص ألوان وخطوط
3. **حفظ كـ PDF**: إمكانية تحميل الفاتورة كملف PDF
4. **طباعة متعددة**: طباعة عدة فواتير مرة واحدة
5. **قوالب مخصصة**: إمكانية إنشاء قوالب مختلفة

### صيانة
- **مراقبة الأخطاء**: تتبع أخطاء الطباعة والمعاينة
- **تحديث المكتبات**: تحديث Print.js عند توفر إصدارات جديدة
- **اختبار التوافق**: اختبار مع متصفحات مختلفة

## الدعم الفني

### مشاكل شائعة
1. **عدم ظهور المعاينة**: تحقق من السماح للنوافذ المنبثقة
2. **مشاكل الطباعة**: تحقق من إعدادات الطابعة
3. **عدم تحميل الشعار**: تحقق من مسار الشعار في إعدادات المتجر

### استكشاف الأخطاء
```javascript
// فحص حالة Print.js
if (typeof printJS === 'undefined') {
    console.error('Print.js library not loaded');
}

// فحص دوال Blazor
if (typeof printReceiptFromBlazor === 'undefined') {
    console.error('Blazor print functions not loaded');
}
```

---

**تاريخ التحديث**: 2025-07-15  
**الإصدار**: 1.0  
**المطور**: Augment Agent
