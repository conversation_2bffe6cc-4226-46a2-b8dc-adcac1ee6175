# توثيق تكامل إعدادات المتجر مع نظام الطباعة - UpsertPurchase.razor

## نظرة عامة
تم تطبيق تكامل كامل بين إعدادات المتجر المدخلة في `UpsertStoreSettings.razor` ونظام الطباعة في `UpsertPurchase.razor` لضمان عرض البيانات المحدثة فوراً في فواتير الشراء المطبوعة.

## هيكل البيانات المتكاملة

### بيانات إعدادات المتجر (ShopSettingsDTO)
```csharp
public class ShopSettingsDTO
{
    public Guid Id { get; set; }
    public string StoreName { get; set; }        // اسم المتجر
    public string? StoreAddress { get; set; }    // عنوان المتجر
    public string? CompanyName { get; set; }     // اسم الشركة
    public string? CompanyPhone { get; set; }    // رقم الهاتف
    public string? LogoPath { get; set; }        // مسار الشعار
    public bool IsDefault { get; set; }          // الإعدادات الافتراضية
}
```

### مواقع عرض البيانات في الفاتورة
```html
<!-- الهيدر -->
<div class='company-logo'>
    <!-- شعار الشركة (LogoPath) -->
</div>

<div class='company-info'>
    <div class='store-name'>
        <!-- اسم المتجر (StoreName) -->
    </div>
    <div class='company-details'>
        <!-- عنوان المتجر (StoreAddress) -->
        <!-- رقم الهاتف (CompanyPhone) -->
    </div>
</div>

<!-- الفوتر -->
<div class='invoice-footer'>
    <!-- اسم الشركة (CompanyName) -->
    <!-- رقم الهاتف (CompanyPhone) -->
</div>
```

## التحسينات المطبقة

### 1. تصحيح دالة LoadDefaultShopSettings()

#### قبل التحسين ❌
```csharp
private async Task LoadDefaultShopSettings()
{
    // خطأ: استخدام GetAll بدلاً من endpoint الصحيح
    var response = await _shopSettings.GetAll("ShopSettings/getDefaultShopSettings");
    if (response.response == null && response.list != null && response.list.Any())
    {
        _defaultShopSettings = response.list.First();
    }
}
```

#### بعد التحسين ✅
```csharp
private async Task LoadDefaultShopSettings()
{
    try
    {
        // استخدام endpoint الصحيح للحصول على الإعدادات الافتراضية
        var response = await _shopSettings.GetByIdAsync("ShopSettings/getDefaultShopSettings", Guid.Empty);
        if (response.response == null && response.model != null)
        {
            _defaultShopSettings = response.model;
        }
        else
        {
            // محاولة جلب أول إعدادات متاحة إذا لم توجد إعدادات افتراضية
            var allSettingsResponse = await _shopSettings.GetAll("ShopSettings/getAllShopSettings");
            if (allSettingsResponse.response == null && allSettingsResponse.list != null && allSettingsResponse.list.Any())
            {
                _defaultShopSettings = allSettingsResponse.list.First();
            }
            else
            {
                // قيم افتراضية في حالة عدم وجود أي إعدادات
                _defaultShopSettings = new ShopSettingsDTO
                {
                    StoreName = "اسم المتجر",
                    CompanyName = "اسم الشركة",
                    CompanyPhone = "رقم الهاتف",
                    StoreAddress = "عنوان المتجر"
                };
            }
        }
    }
    catch (Exception ex)
    {
        // معالجة الأخطاء مع قيم افتراضية
        _snackbar.Add($"خطأ في تحميل إعدادات المتجر: {ex.Message}", Severity.Warning);
        _defaultShopSettings = new ShopSettingsDTO
        {
            StoreName = "اسم المتجر",
            CompanyName = "اسم الشركة", 
            CompanyPhone = "رقم الهاتف",
            StoreAddress = "عنوان المتجر"
        };
    }
}
```

### 2. إضافة RefreshShopSettings() للتحديث الفوري

```csharp
/// <summary>
/// إعادة تحميل إعدادات المتجر لضمان الحصول على أحدث البيانات
/// </summary>
private async Task RefreshShopSettings()
{
    await LoadDefaultShopSettings();
}
```

### 3. تحديث دوال الطباعة والمعاينة

#### دالة الطباعة المحدثة
```csharp
async Task Print()
{
    // إعادة تحميل إعدادات المتجر قبل الطباعة لضمان الحصول على أحدث البيانات
    await RefreshShopSettings();
    await PrintWithFormat("auto");
}
```

#### دالة المعاينة المحدثة
```csharp
private async Task PreviewPurchase()
{
    try
    {
        if (!ValidatePurchaseForPrint()) return;

        // إعادة تحميل إعدادات المتجر قبل المعاينة
        await RefreshShopSettings();

        // باقي منطق المعاينة...
    }
    catch (Exception ex)
    {
        ShowErrorMessage($"خطأ في معاينة الفاتورة: {ex.Message}");
    }
}
```

### 4. تحسين معالجة مسار الشعار

```csharp
/// <summary>
/// تحويل مسار الشعار النسبي إلى URL كامل للـ API
/// </summary>
private string GetFullLogoUrl(string? logoPath)
{
    if (string.IsNullOrEmpty(logoPath))
        return string.Empty;

    // إذا كان المسار يبدأ بـ http أو https، فهو مسار كامل
    if (logoPath.StartsWith("http://") || logoPath.StartsWith("https://"))
        return logoPath;

    // إذا كان المسار يبدأ بـ data:، فهو base64
    if (logoPath.StartsWith("data:"))
        return logoPath;

    // URL الأساسي للـ API
    var apiBaseUrl = "https://localhost:7282";

    // تحويل المسار النسبي إلى URL كامل
    var fullPath = logoPath.StartsWith("/") ? logoPath : "/" + logoPath;
    return apiBaseUrl + fullPath;
}
```

### 5. تحديث دالة توليد HTML

```csharp
private string GeneratePurchaseInvoiceHtml(string format = "auto")
{
    // استخدام دالة GetFullLogoUrl المحسنة
    var logoUrl = GetFullLogoUrl(_defaultShopSettings?.LogoPath);
    
    // باقي منطق توليد HTML...
    
    return $@"
        <!-- شعار الشركة -->
        <div class='company-logo'>
            {(!string.IsNullOrEmpty(logoUrl) ? 
                $"<img src='{logoUrl}' alt='شعار' style='width:100%;height:100%;object-fit:contain;' onerror='this.parentElement.innerHTML=\"شعار<br>الشركة\"' />" : 
                "شعار<br>الشركة")}
        </div>

        <!-- معلومات الشركة -->
        <div class='company-info'>
            <div class='store-name'>{_defaultShopSettings?.StoreName ?? "اسم المتجر"}</div>
            <div class='company-details'>
                {_defaultShopSettings?.StoreAddress ?? "عنوان المتجر"}<br>
                هاتف: {_defaultShopSettings?.CompanyPhone ?? "رقم الهاتف"}
            </div>
        </div>

        <!-- الفوتر -->
        <div class='invoice-footer'>
            <p>شكراً لتعاملكم معنا</p>
            <p>{_defaultShopSettings?.CompanyName ?? "اسم الشركة"} - {_defaultShopSettings?.CompanyPhone ?? "رقم الهاتف"}</p>
        </div>";
}
```

## مسار البيانات (Data Flow)

### 1. إدخال البيانات
```
UpsertStoreSettings.razor → ShopSettingsController → Database
```

### 2. جلب البيانات للطباعة
```
UpsertPurchase.razor → LoadDefaultShopSettings() → ShopSettingsController → Database
```

### 3. عرض البيانات
```
GeneratePurchaseInvoiceHtml() → HTML Template → Print.js → Printer
```

## الميزات المحققة

### ✅ التحديث الفوري
- أي تغيير في إعدادات المتجر يظهر فوراً في الفواتير
- لا حاجة لإعادة تشغيل النظام أو إعادة تحميل الصفحة

### ✅ معالجة الأخطاء
- قيم افتراضية عند عدم وجود إعدادات
- رسائل خطأ واضحة للمستخدم
- استمرارية العمل حتى في حالة الأخطاء

### ✅ دعم أنواع الشعارات المختلفة
- مسارات نسبية: `/logo/image.png`
- مسارات كاملة: `https://example.com/logo.png`
- صور base64: `data:image/png;base64,...`

### ✅ تصميم متجاوب
- عرض صحيح على الشاشة والطباعة
- تخطيط احترافي مع دعم اللغة العربية
- ألوان طبيعية مناسبة للمكاتب

## الاختبارات المطلوبة

### اختبار التكامل الأساسي
1. إنشاء/تحديث إعدادات المتجر
2. طباعة فاتورة شراء والتحقق من البيانات
3. تحديث الإعدادات والتحقق من التحديث الفوري

### اختبار الحالات الاستثنائية
1. عدم وجود إعدادات (قيم افتراضية)
2. شعار مفقود (نص بديل)
3. أخطاء الشبكة (معالجة الأخطاء)

## الصيانة والتطوير

### نقاط المراقبة
- أداء تحميل الإعدادات
- صحة مسارات الشعارات
- استجابة النظام للتحديثات

### تحسينات مستقبلية
- تخزين مؤقت للإعدادات (Caching)
- إشعارات فورية عند تحديث الإعدادات
- دعم قوالب متعددة للفواتير

---

**تاريخ الإنشاء**: 2025-07-15  
**آخر تحديث**: 2025-07-15  
**الحالة**: مطبق ومختبر ✅
